package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.OceanStationData;
import cn.piesat.data.making.server.vo.OceanStationDataVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 海洋站数据表表数据库访问层
 *
 * <AUTHOR>
 */
public interface OceanStationDataDao extends BaseMapper<OceanStationData> {

    @Select({"<script>",
            "SELECT osd.ocean_station_code oceanStationCode, os.cn_name oceanStationName, os.location_json oceanStationLocationJson, osd.time, " +
                    "osd.wind_speed windSpeed, osd.wind_dir windDir, osd.sea_temperature seaTemperature, osd.wind_wave_height windWaveHeight, " +
                    "osd.wind_wave_period windWavePeriod " +
                    "FROM fm_ocean_station_b os " +
                    "inner join fm_ocean_station_data_b osd on os.code = osd.ocean_station_code " +
                    "WHERE 1=1 " +
                    "<if test='startTime != null'>" +
                    "and osd.time &gt;= #{startTime} " +
                    "</if>" +
                    "<if test='endTime != null'>" +
                    "and osd.time &lt;= #{endTime} " +
                    "</if>" +
                    "<if test='geoRange != null'>" +
                    "and ST_Contains(ST_GeomFromText(#{geoRange},4326), os.location_geo) " +
                    "</if>" +
                    "<if test='oceanStationCodeList != null'>" +
                    "and os.code in " +
                    "<foreach collection='oceanStationCodeList' item='code' open='(' separator=',' close=')'> " +
                    "#{code}" +
                    "</foreach>" +
                    "</if> order by osd.time",
            "</script>"})
    List<OceanStationDataVO> getDataList(Date startTime, Date endTime, String geoRange, List<String> oceanStationCodeList);
}
