package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预报记录详情表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ForecastRecordDetailDTO implements Serializable {

    private static final long serialVersionUID = 288906695176836302L;

    public interface Save {
    }

    /**
     * id
     **/
    private Long id;
    /**
     * 预报记录id
     **/
    private Long forecastRecordId;
    /**
     * 区域id
     **/
    private Long areaId;
    /**
     * 区域编码
     **/
    private String areaCode;
    /**
     * 区域名称
     **/
    private String areaName;
    /**
     * 要素编码
     **/
    private String elementCode;
    /**
     * 要素名称
     **/
    private String elementName;
    /**
     * 列编码
     **/
    private String columnCode;
    /**
     * 列名
     **/
    private String columnName;
    /**
     * 要素数值处理
     **/
    private String elementValueHandle;
    /**
     * 要素是否显示
     **/
    private Boolean elementDisplay;
    /**
     * 值
     **/
    private String value;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}



