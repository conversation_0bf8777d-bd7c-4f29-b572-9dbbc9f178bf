package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 行政区表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RegionDTO implements Serializable {

    private static final long serialVersionUID = 727967926543408842L;

    public interface Save {
    }

    /**
     * id
     **/
    private Long id;
    /**
     * 编码
     **/
    @NotBlank(message = "编码不能为空", groups = {Save.class})
    private String code;
    /**
     * 名称
     **/
    @NotBlank(message = "名称不能为空", groups = {Save.class})
    private String name;
    /**
     * 父级编码
     **/
    private String parentCode;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}



