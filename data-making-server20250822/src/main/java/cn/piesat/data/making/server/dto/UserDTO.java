package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UserDTO implements Serializable {

    private static final long serialVersionUID = 103580327937331003L;

    public interface Save {
    }

    /**
     * id
     **/
    private Long id;
    /**
     * 标签id
     **/
    @NotNull(message = "标签id不能为空", groups = {Save.class})
    private Long tagId;
    /**
     * 名称
     **/
    @NotBlank(message = "名称不能为空", groups = {Save.class})
    private String name;
    /**
     * 联系电话
     **/
    private String phone;
    /**
     * 电子邮箱
     **/
    private String email;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}



