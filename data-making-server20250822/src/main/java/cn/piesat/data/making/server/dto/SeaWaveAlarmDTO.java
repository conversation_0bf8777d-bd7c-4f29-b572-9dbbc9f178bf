package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown=true)
public class SeaWaveAlarmDTO {
    private Long id;
    /**
     * 历史预警
     */
    private String historyAlarmLevel;
    /**
     * 预警级别
     */
    @NotNull(message = "预警级别不能为空")
    private Long alarmLevel;
    /**
     * 标题
     */
    @NotEmpty(message = "标题不能为空")
    private String title;
    /**
     * 发布时间
     */
    @NotNull(message = "发布时间不能为空")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date releaseTime;
    /**
     * 编号
     */
    @NotEmpty(message = "编号不能为空")
    private String number;
    /**
     * 上期编号
     */
    private String lastNumber;
    /**
     * 签发人
     */
    @NotNull(message = "签发人不能为空")
    private Long signUser;
    /**
     * 制作人
     */
    @NotNull(message = "制作人不能为空")
    private Long makeUser;
    /**
     * 综述
     */
    @NotEmpty(message = "综述不能为空")
    private String summarize;
    /**
     * 警报内容
     */
    @NotEmpty(message = "警报内容不能为空")
    private String alarmContent;
    /**
     * 防御指南
     */
    @NotEmpty(message = "防御指南不能为空")
    private String defenseGuide;
    /**
     * 短信
     */
    @NotEmpty(message = "短信不能为空")
    private String smsContent;
    /**
     * 警报图
     */
//    @NotEmpty(message = "警报图不能为空")
    private String alarmImages;

    /**
     * 台风编号
     */
    private String typhoonNo;
    /**
     * 台风时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date typhoonTime;
    /**
     * 签发人
     */
    @NotEmpty(message = "签发人名不能为空")
    private String signUserName;
    /**
     * 制作人
     */
    @NotEmpty(message = "制作人名不能为空")
    private String makeUserName;
    /**
     * 源头类型  1冷空气 2台风
     */
    @NotNull(message = "源类型不能为空")
    private Integer sourceType;
    /**
     * 预警区域
     */
//    @NotBlank(message = "预警区域不能为空")
    private String alarmArea;

    /**
     * 城市警报内容
     */
    private String cityAlarmContent;

    /**
     * 警报时段
     */
    private String alarmTime;

    public static final Integer TYPHOON = 2;
    public static final Integer COLD_AIR = 1;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHistoryAlarmLevel() {
        return historyAlarmLevel;
    }

    public void setHistoryAlarmLevel(String historyAlarmLevel) {
        this.historyAlarmLevel = historyAlarmLevel;
    }

    public Long getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(Long alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getLastNumber() {
        return lastNumber;
    }

    public void setLastNumber(String lastNumber) {
        this.lastNumber = lastNumber;
    }

    public Long getSignUser() {
        return signUser;
    }

    public void setSignUser(Long signUser) {
        this.signUser = signUser;
    }

    public Long getMakeUser() {
        return makeUser;
    }

    public void setMakeUser(Long makeUser) {
        this.makeUser = makeUser;
    }

    public String getSummarize() {
        return summarize;
    }

    public void setSummarize(String summarize) {
        this.summarize = summarize;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getDefenseGuide() {
        return defenseGuide;
    }

    public void setDefenseGuide(String defenseGuide) {
        this.defenseGuide = defenseGuide;
    }

    public String getSmsContent() {
        return smsContent;
    }

    public void setSmsContent(String smsContent) {
        this.smsContent = smsContent;
    }

    public String getAlarmImages() {
        return alarmImages;
    }

    public void setAlarmImages(String alarmImages) {
        this.alarmImages = alarmImages;
    }

    public String getTyphoonNo() {
        return typhoonNo;
    }

    public void setTyphoonNo(String typhoonNo) {
        this.typhoonNo = typhoonNo;
    }

    public Date getTyphoonTime() {
        return typhoonTime;
    }

    public void setTyphoonTime(Date typhoonTime) {
        this.typhoonTime = typhoonTime;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }

    public String getMakeUserName() {
        return makeUserName;
    }

    public void setMakeUserName(String makeUserName) {
        this.makeUserName = makeUserName;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getAlarmArea() {
        return alarmArea;
    }

    public void setAlarmArea(String alarmArea) {
        this.alarmArea = alarmArea;
    }

    public String getCityAlarmContent() {
        return cityAlarmContent;
    }

    public void setCityAlarmContent(String cityAlarmContent) {
        this.cityAlarmContent = cityAlarmContent;
    }

    public String getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(String alarmTime) {
        this.alarmTime = alarmTime;
    }
}
