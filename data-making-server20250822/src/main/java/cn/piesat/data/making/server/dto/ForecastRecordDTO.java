package cn.piesat.data.making.server.dto;

import cn.piesat.data.making.server.entity.ForecastRecordDetail;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预报记录表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ForecastRecordDTO implements Serializable {

    private static final long serialVersionUID = 951504920988122224L;

    public interface Save {
    }

    public interface Submit {
    }

    /**
     * id
     **/
    private Long id;
    /**
     * 预报任务id
     **/
    @NotNull(message = "预报任务id不能为空", groups = {Save.class, Submit.class})
    private Long forecastTaskId;
    /**
     * 预报模板id
     **/
    @NotNull(message = "预报模板id不能为空", groups = {Submit.class})
    private Long forecastTemplateId;
    /**
     * 预报模板编码
     **/
    @NotNull(message = "预报模板编码不能为空", groups = {Submit.class})
    private String forecastTemplateCode;
    /**
     * 预报类型
     **/
    @NotBlank(message = "预报类型不能为空", groups = {Submit.class})
    private String forecastType;
    /**
     * 起报时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "起报时间不能为空", groups = {Save.class, Submit.class})
    private Date reportTime;
    /**
     * 数据源
     **/
    @NotBlank(message = "数据源不能为空", groups = {Save.class, Submit.class})
    private String dataSource;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 预报记录明细列表
     **/
    @NotEmpty(message = "预报记录明细列表不能为空", groups = {Save.class, Submit.class})
    private List<ForecastRecordDetail> detailList;

    /**
     * 海区、城市预报制作-预报内容
     */
    private String forecastContent;
}



