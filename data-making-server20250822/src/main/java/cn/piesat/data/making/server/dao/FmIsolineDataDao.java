package cn.piesat.data.making.server.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;
import cn.piesat.data.making.server.entity.FmIsolineData;

import java.util.Date;
import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-10-15 10:07:30
 */
public interface FmIsolineDataDao extends BaseMapper<FmIsolineData> {
    @Select("SELECT * FROM fm_isoline_data where data_source = #{dataSource} and start_report_time =#{date}")
    List<FmIsolineData> selectByDataSourceAndDate(String dataSource, Date date);

}
