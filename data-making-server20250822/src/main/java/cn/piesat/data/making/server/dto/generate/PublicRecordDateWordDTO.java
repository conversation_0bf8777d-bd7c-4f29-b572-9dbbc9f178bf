package cn.piesat.data.making.server.dto.generate;

import java.util.Date;

public class PublicRecordDateWordDTO {

    private Long id;

    private Long recordId;

    private Date tideTime;

    private String tideTimeDate;

    private String tideTimeTime;

    private Integer height;

    private Integer type;

    private String stationName;

    private Long stationId;

    private Date createTime;

    private Date updateTime;

    private String datum;

    private Integer warnHeight;

    private Integer level;

    private Integer datumBlueDif;

    private Integer datumBlueWarn;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Date getTideTime() {
        return tideTime;
    }

    public void setTideTime(Date tideTime) {
        this.tideTime = tideTime;
    }

    public String getTideTimeDate() {
        return tideTimeDate;
    }

    public void setTideTimeDate(String tideTimeDate) {
        this.tideTimeDate = tideTimeDate;
    }

    public String getTideTimeTime() {
        return tideTimeTime;
    }

    public void setTideTimeTime(String tideTimeTime) {
        this.tideTimeTime = tideTimeTime;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDatum() {
        return datum;
    }

    public void setDatum(String datum) {
        this.datum = datum;
    }

    public Integer getWarnHeight() {
        return warnHeight;
    }

    public void setWarnHeight(Integer warnHeight) {
        this.warnHeight = warnHeight;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getDatumBlueDif() {
        return datumBlueDif;
    }

    public void setDatumBlueDif(Integer datumBlueDif) {
        this.datumBlueDif = datumBlueDif;
    }

    public Integer getDatumBlueWarn() {
        return datumBlueWarn;
    }

    public void setDatumBlueWarn(Integer datumBlueWarn) {
        this.datumBlueWarn = datumBlueWarn;
    }
}
