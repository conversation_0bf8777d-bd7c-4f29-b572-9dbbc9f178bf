package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.GroupOceanLargeDeepseaMooringbuoyO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 深海大型锚系浮标原始数据表数据库访问层
 *
 * <AUTHOR>
 */
public interface GroupOceanLargeDeepseaMooringbuoyODao extends BaseMapper<GroupOceanLargeDeepseaMooringbuoyO> {

    @Select({"<script>",
            "select * from group_ocean_large_deepsea_mooringbuoy_o where monitoringtime between #{startTime} and #{endTime} " +
                    "and buoyinfo_id in " +
                    "<foreach collection='stationNumList' item='stationNum' open='(' separator=',' close=')'> " +
                    "#{stationNum}" +
                    "</foreach>",
            "</script>"})
    List<GroupOceanLargeDeepseaMooringbuoyO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    @Select("SELECT MAX(createtime) FROM group_ocean_large_deepsea_mooringbuoy_o")
    LocalDateTime getMaxCreateTime();

    @Select("select distinct buoyinfo_id from group_ocean_large_deepsea_mooringbuoy_o")
    @MapKey("buoyinfo_id")
    List<String> getBuoyIdList();
}
