package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmGraphicSign;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.piesat.data.making.server.entity.FmGraphicTools;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:38
 */
public interface FmGraphicToolsDao extends BaseMapper<FmGraphicTools> {
    @Select("SELECT * FROM fm_graphic_tools order by id")
    List<FmGraphicTools> selectAll();
}
