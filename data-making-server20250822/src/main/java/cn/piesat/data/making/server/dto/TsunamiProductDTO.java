package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 海啸产品表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TsunamiProductDTO implements Serializable {

    private static final long serialVersionUID = 832122004807388426L;

    public interface Save {
    }

    /**
     * id
     **/
    private Long id;
    /**
     * 名称
     **/
    private String name;
    /**
     * 文件地址
     **/
    private String fileUrl;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * ids
     **/
    private String ids;
    /**
     * 开始时间
     **/
    private Date startTime;
    /**
     * 结束时间
     **/
    private Date endTime;
}



