package cn.piesat.data.making.server.dto.generate;

import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.TableRenderData;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ForecastSpecialWordDTO {

    private String releaseTime;
    private String productName;
    private String elementName;
    private TableRenderData tableList;
    //海浪注释
    private String waveAnnotation;
    //防护措施建议
    private String suggestion;
    //牧场名称
    private String ranchName;
    private String ranchValue;
    private List<PictureRenderData> images;
}
