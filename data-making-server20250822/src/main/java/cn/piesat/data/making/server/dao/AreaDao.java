package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.Area;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 区域表表数据库访问层
 *
 * <AUTHOR>
 */
public interface AreaDao extends BaseMapper<Area> {

    @Update("update fm_area_b set code = #{code}, location_geo = st_geomfromgeojson(#{locationGeo}), location_json = #{locationJson} WHERE name = #{name}")
    void updateLocationByName(String code, String name, String locationGeo, String locationJson);

    String selectGeoJsonByIds(@Param("areaIds") List<Long> areaIds);

}
