package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmGraphicRecordB;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Update;

/**
 * 图形记录表数据库访问层
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:37
 */
public interface FmGraphicRecordBDao extends BaseMapper<FmGraphicRecordB> {
    @Update("UPDATE fm_graphic_record_b set checked = 'false' WHERE id != #{id}")
    void updateCheckStatusById(Long id);
}
