package cn.piesat.data.making.server.dto;

import cn.piesat.data.making.server.entity.ForecastTemplateColumn;
import cn.piesat.data.making.server.entity.ForecastTemplateRow;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预报模板表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ForecastTemplateDTO implements Serializable {

    private static final long serialVersionUID = 822510695934692926L;

    public interface Save {
    }

    /**
     * id
     **/
    private Long id;
    /**
     * 编码
     **/
    private String code;
    /**
     * 名称
     **/
    @NotBlank(message = "名称不能为空", groups = {Save.class})
    private String name;
    /**
     * 预报类型
     **/
    @NotBlank(message = "预报类型不能为空", groups = {Save.class})
    private String forecastType;
    /**
     * 状态
     **/
    private Boolean status;
    /**
     * 排序
     **/
    @NotNull(message = "排序不能为空", groups = {Save.class})
    private Integer sort;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 行
     */
    private List<ForecastTemplateRow> rowList;
    /**
     * 列
     */
    private List<ForecastTemplateColumn> columnList;
}



