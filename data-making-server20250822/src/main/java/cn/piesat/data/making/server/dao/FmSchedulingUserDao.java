package cn.piesat.data.making.server.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;
import cn.piesat.data.making.server.entity.FmSchedulingUser;

import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-10-30 16:08:29
 */
public interface FmSchedulingUserDao extends BaseMapper<FmSchedulingUser> {
    @Select("SELECT * FROM fm_scheduling_user WHERE user_id = #{id}")
    FmSchedulingUser findByUserId(Long id);
    @Select("SELECT * FROM fm_scheduling_user")
    List<FmSchedulingUser> selectAll();
    @Select("SELECT * FROM fm_scheduling_user where user_name = #{name}")
    FmSchedulingUser findByUserName(String name);

}
