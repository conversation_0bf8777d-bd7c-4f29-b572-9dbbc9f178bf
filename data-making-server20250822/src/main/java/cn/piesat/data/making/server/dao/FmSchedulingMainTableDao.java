package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmGraphicTemplateB;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.piesat.data.making.server.entity.FmSchedulingMainTable;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:01
 */
public interface FmSchedulingMainTableDao extends BaseMapper<FmSchedulingMainTable> {
    @Select("SELECT * FROM fm_scheduling_main_table where scheduling_date >= #{date1} and scheduling_date <= #{date2}")
    FmSchedulingMainTable findByDate(Date date1, Date date2);
}
