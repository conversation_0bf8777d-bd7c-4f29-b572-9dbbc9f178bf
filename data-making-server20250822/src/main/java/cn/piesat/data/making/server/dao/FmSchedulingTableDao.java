package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmSchedulingMainTable;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.piesat.data.making.server.entity.FmSchedulingTable;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-09-27 16:37:05
 */
public interface FmSchedulingTableDao extends BaseMapper<FmSchedulingTable> {
    @Select("SELECT * FROM fm_scheduling_table WHERE DATE(scheduling_date) = #{date}")
    FmSchedulingTable finBySchedulingDate(Date date);

    @Delete("DELETE FROM fm_scheduling_table WHERE scheduling_main_id = #{mainId}")
    void deleteBySchedulingMainId(Long mainId);
    @Select("SELECT * FROM fm_scheduling_table WHERE scheduling_main_id = #{mainId}")
    List<FmSchedulingTable> findByMainId(Long mainId);
}
