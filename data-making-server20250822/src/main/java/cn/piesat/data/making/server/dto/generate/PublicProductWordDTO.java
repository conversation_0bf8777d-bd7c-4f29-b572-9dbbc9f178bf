package cn.piesat.data.making.server.dto.generate;

import cn.piesat.data.making.server.vo.FmPublicRecordDataVO;

import java.util.List;

public class PublicProductWordDTO {

    private String time;

    private String number;

    private String month;

    private List<PublicRecordDateWordDTO> list;

    private String contentF;

    private String contentS;

    private String contentT;

    private String makeUserName;

    private String phone;

    private String fax;

    private String eMail;

    private String signUserName;


    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public List<PublicRecordDateWordDTO> getList() {
        return list;
    }

    public void setList(List<PublicRecordDateWordDTO> list) {
        this.list = list;
    }

    public String getContentF() {
        return contentF;
    }

    public void setContentF(String contentF) {
        this.contentF = contentF;
    }

    public String getContentS() {
        return contentS;
    }

    public void setContentS(String contentS) {
        this.contentS = contentS;
    }

    public String getContentT() {
        return contentT;
    }

    public void setContentT(String contentT) {
        this.contentT = contentT;
    }

    public String getMakeUserName() {
        return makeUserName;
    }

    public void setMakeUserName(String makeUserName) {
        this.makeUserName = makeUserName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String geteMail() {
        return eMail;
    }

    public void seteMail(String eMail) {
        this.eMail = eMail;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }
}
