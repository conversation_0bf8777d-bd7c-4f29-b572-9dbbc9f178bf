package cn.piesat.data.making.server.dto.generate;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class WaveForecastDataDTO implements Serializable {
    private String dataSource = "forecast";

    private String elementCode = "swh";

    private String productCode = "regWave";

    private List<Long> areaIds;

    private Date startTime;

    private Date forecastStartTime;

    private Date forecastEndTime;

    public WaveForecastDataDTO() {

    }

    public WaveForecastDataDTO(List<Long> areaIds, Date startTime, Date forecastStartTime, Date forecastEndTime) {
        this.areaIds = areaIds;
        this.startTime = startTime;
        this.forecastStartTime = forecastStartTime;
        this.forecastEndTime = forecastEndTime;
    }
}
