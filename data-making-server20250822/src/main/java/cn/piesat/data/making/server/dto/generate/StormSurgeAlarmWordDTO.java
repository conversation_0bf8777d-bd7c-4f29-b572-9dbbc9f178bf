package cn.piesat.data.making.server.dto.generate;

import cn.piesat.data.making.server.dto.StormSurgeAlarmDTO;
import com.deepoove.poi.data.PictureRenderData;
import lombok.Data;

import java.util.List;

@Data
public class StormSurgeAlarmWordDTO {
    private String levelColor;
    private String time;
    private String number;
    private String signUserName;
    private String title;
    private String summarize;
    private String alarmContent;
    private String defenseGuide;
    private List<PictureRenderData> images;
    private String makeUserName;
    private String phone;
    private String fax;
    private String eMail;
    private List<StormSurgeAlarmDTO.TideDailyWarnLevelDTO> stationWarning;
}
