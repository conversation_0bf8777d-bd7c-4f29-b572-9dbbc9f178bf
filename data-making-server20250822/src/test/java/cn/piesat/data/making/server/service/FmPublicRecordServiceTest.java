package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.vo.FmPublicRecordDataVO;
import cn.piesat.data.making.server.vo.FmPublicRecordVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPublicRecordServiceTest {

    @Autowired
    private FmPublicRecordService fmPublicRecordServiceImpl;

    @Test
    public void testSave(){
        FmPublicRecordVO fmPublicRecordVO = new FmPublicRecordVO();
        fmPublicRecordVO.setTaskId(1944444969989189634L);
        fmPublicRecordVO.setTemplateId(1944440178688962562L);
        fmPublicRecordVO.setPublicType("1");

        List<FmPublicRecordDataVO> list = new ArrayList<>();
        for(int i=0;i<10;i++){
            FmPublicRecordDataVO vo = new FmPublicRecordDataVO();
            vo.setHeight(i);
            list.add(vo);
        }

        fmPublicRecordVO.setList(list);

        fmPublicRecordServiceImpl.save(fmPublicRecordVO);
    }

    @Test
    public void testGetLastRecord(){
        FmPublicRecordVO fmPublicRecordVO = fmPublicRecordServiceImpl.getLastRecord("1");

        Assert.assertNotNull(fmPublicRecordVO);
    }

    @Test
    public void testMakeFile(){
        FmPublicRecordVO vo = fmPublicRecordServiceImpl.getByPk(1944483177703784450L);
        fmPublicRecordServiceImpl.makeFile(vo);
    }

    @Test
    public void testSaveAndMakeFile(){
        FmPublicRecordVO fmPublicRecordVO = fmPublicRecordServiceImpl.getByPk(1944483177703784450L);

        fmPublicRecordServiceImpl.update(fmPublicRecordVO);
    }
}
