package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.entity.FmPublicRecordData;
import cn.piesat.data.making.server.vo.FmPublicRecordDataVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPublicRecordDataServiceTest {

    @Autowired
    private FmPublicRecordDataService fmPublicRecordDataServiceImpl;

    @Test
    public void testSaveList(){
        List<FmPublicRecordDataVO> list = new ArrayList<>();

        FmPublicRecordDataVO fmPublicRecordDataVO = null;
        for(long i=1;i<50;i++){
            fmPublicRecordDataVO = new FmPublicRecordDataVO();

            fmPublicRecordDataVO.setRecordId(i);
            list.add(fmPublicRecordDataVO);
        }
        fmPublicRecordDataServiceImpl.saveList(list);
    }

    @Test
    public void testDeleteAll(){
        fmPublicRecordDataServiceImpl.deleteAll(1L);
    }

    @Test
    public void testDelete(){
        fmPublicRecordDataServiceImpl.delete(1L);
    }
}
