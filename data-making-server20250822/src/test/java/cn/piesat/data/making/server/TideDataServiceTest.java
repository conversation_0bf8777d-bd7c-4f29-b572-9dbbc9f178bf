package cn.piesat.data.making.server;

import cn.piesat.data.making.server.service.TideService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class TideDataServiceTest {

    @Resource
    private TideService tideService;


    @Test
    public void parseAndSaveTest() throws Exception {
        String filePath = "D:\\项目文档\\hzxm\\2024\\2024\\海南省海洋监测预报中心-2024年潮汐预报\\理论深度基准面起算预报数据\\清澜";
        tideService.parseAndSaveTideData(filePath);
    }

    @Test
    public void parseAndSave() throws Exception {
        File file = new File("C:\\Users\\<USER>\\Desktop\\海南省海洋监测预报中心-2025年潮汐预报\\85");
        File[] filePathLists = file.listFiles();
        for (File o : filePathLists) {
            tideService.parseAndSaveTideData(o.getAbsolutePath());
        }
    }

    @Test
    public void updateWarn(){
        tideService.updateWarnHeight();
    }
}
