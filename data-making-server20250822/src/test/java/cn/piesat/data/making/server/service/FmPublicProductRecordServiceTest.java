package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.DataMakingServerApplication;
import cn.piesat.data.making.server.vo.FmPublicProductRecordVO;
import cn.piesat.data.making.server.vo.FmPublicRecordVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DataMakingServerApplication.class)
@RunWith(SpringRunner.class)
@AutoConfigureMockMvc
public class FmPublicProductRecordServiceTest {

    @Autowired
    private FmPublicProductRecordService fmPublicProductRecordServiceImpl;

    @Autowired
    private FmPublicRecordService fmPublicRecordServiceImpl;

    @Test
    public void testFindById(){
        FmPublicProductRecordVO vo = fmPublicProductRecordServiceImpl.findById(1944425734357504002L);

        Assert.assertNotNull(vo);
    }

    @Test
    public void testMakeFile(){
        FmPublicRecordVO vo = fmPublicRecordServiceImpl.getByPk(1944483177703784450L);
        //fmPublicProductRecordServiceImpl.makeFile(vo);
    }

    @Test
    public void testFindByRecordId(){
        FmPublicProductRecordVO vo = fmPublicProductRecordServiceImpl.findByRecordId(123L);
        Assert.assertNotNull(vo);
    }
}
